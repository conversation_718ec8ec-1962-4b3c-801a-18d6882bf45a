import os
import logging
import time
import ffmpeg
from io import BytesIO
from minio import Minio
from funasr import AutoModel

from celery_app import celery_app

# --- 日志配置 ---
# Worker日志建议也独立配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- 模型加载 ---
# 这部分代码只在Celery Worker启动时执行一次，而不是每次任务都执行
logger.info("Initializing ASR Worker...")
logger.info("Loading FunASR model from local paths... This may take a while.")

# The container will have the models mounted at /models.
# These environment variables should point to the specific model directories inside the container.
ASR_MODEL_PATH = os.getenv("ASR_MODEL_PATH", "/models/paraformer-zh")
VAD_MODEL_PATH = os.getenv("VAD_MODEL_PATH", "/models/fsmn-vad")
PUNC_MODEL_PATH = os.getenv("PUNC_MODEL_PATH", "/models/ct-punc-c")
SPK_MODEL_PATH = os.getenv("SPK_MODEL_PATH", "/models/cam++")

NGPU = int(os.getenv("NGPU", 1))
DEVICE = "cuda" if NGPU > 0 else "cpu"
NCPU = int(os.getenv("NCPU", 4)) # Worker的CPU核心数可以少一些
HOTWORD_PATH = os.getenv("HOTWORD_PATH", "hotwords.txt")

model = AutoModel(
    model=ASR_MODEL_PATH,
    vad_model=VAD_MODEL_PATH,
    punc_model=PUNC_MODEL_PATH,
    spk_model=SPK_MODEL_PATH,
    ngpu=NGPU,
    ncpu=NCPU,
    device=DEVICE,
    disable_pbar=True,
    disable_log=True,
    disable_update=True,
)
logger.info("FunASR model loaded successfully!")

param_dict = {"sentence_timestamp": True, "batch_size_s": 300}
if os.path.exists(HOTWORD_PATH):
    with open(HOTWORD_PATH, "r", encoding="utf-8") as f:
        hotword = " ".join([line.strip() for line in f])
    logger.info(f"Loaded hotwords: {hotword}")
    param_dict["hotword"] = hotword

# --- MinIO 客户端配置 ---
MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT", "localhost:9000")
MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "minioadmin")

minio_client = Minio(
    MINIO_ENDPOINT,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=False
)

@celery_app.task(bind=True, name='asr_worker.asr_task')
def asr_task(self, bucket_name: str, object_name: str):
    """
    Celery task for ASR processing.
    `bind=True` allows accessing the task instance via `self`.
    """
    task_id = self.request.id
    logger.info(f"[{task_id}] Received task: process {object_name} from bucket {bucket_name}")

    try:
        # 1. 从MinIO下载音频文件到内存
        self.update_state(state='PROGRESS', meta={'status': 'Downloading audio file...'})
        response = minio_client.get_object(bucket_name, object_name)
        audio_data = response.read()
        response.close()
        response.release_conn()
        logger.info(f"[{task_id}] Audio file downloaded from MinIO.")

        # 2. 使用ffmpeg进行格式转换
        self.update_state(state='PROGRESS', meta={'status': 'Converting audio format...'})
        audio_bytes, _ = (
            ffmpeg.input("pipe:0")
            .output("-", format="s16le", acodec="pcm_s16le", ac=1, ar=16000)
            .run(input=audio_data, capture_stdout=True, capture_stderr=True)
        )
        logger.info(f"[{task_id}] Audio converted to PCM s16le.")

        # 3. 执行ASR推理
        self.update_state(state='PROGRESS', meta={'status': 'Running ASR model...'})
        start_time = time.time()
        rec_results = model.generate(input=audio_bytes, is_final=True, **param_dict)
        end_time = time.time()
        logger.info(f"[{task_id}] ASR model processing finished in {end_time - start_time:.2f}s.")

        # 4. 处理并返回结果
        if not rec_results:
            logger.warning(f"[{task_id}] Recognition result is empty.")
            return {"status": 1, "message": "Recognition result is empty", "data": {}}

        rec_result = rec_results[0]
        sentences = [
            {
                "text": s.get("text", ""),
                "start": s.get("start"),
                "end": s.get("end"),
                "timestamp": s.get("timestamp", []),
                "spk": s.get("spk", 0)
            }
            for s in rec_result.get("sentence_info", [])
        ]
        result_data = {"text": rec_result.get("text", ""), "sentences": sentences}
        logger.info(f"[{task_id}] Recognition successful.")
        
        return {"status": 0, "message": "success", "data": result_data}

    except ffmpeg.Error as e:
        logger.error(f"[{task_id}] FFmpeg error: {e.stderr.decode()}", exc_info=True)
        # self.update_state(state='FAILURE', meta={'exc_type': 'FFmpegError', 'exc_message': e.stderr.decode()})
        raise Exception(f"FFmpeg error: {e.stderr.decode()}") # Re-raise to mark task as failed
    except Exception as e:
        logger.error(f"[{task_id}] An unexpected error occurred: {e}", exc_info=True)
        # self.update_state(state='FAILURE', meta={'exc_type': str(type(e).__name__), 'exc_message': str(e)})
        raise e # Re-raise to mark task as failed

# 如何启动Worker:
# celery -A celery_app worker --loglevel=info --concurrency=1 -Q celery
# --concurrency=1 : 对于GPU任务，通常一个worker独占一个GPU，所以并发设为1
# -Q celery : 指定消费的队列名称，默认为celery

# download_models.py
import os
from funasr import AutoModel

# --- Configuration ---
# This is the directory on your HOST machine where models will be stored.
# Using an absolute path is recommended.
model_cache_dir = "/opt/asr_models"
os.makedirs(model_cache_dir, exist_ok=True)

print(f"Models will be downloaded to: {model_cache_dir}")

# --- List of models used by the asr_worker ---
model_names = {
    "asr": "paraformer-zh",
    "asr_revision": "v2.0.4",
    "vad": "fsmn-vad",
    "vad_revision": "v2.0.4",
    "punc": "ct-punc-c",
    "punc_revision": "v2.0.4",
    "spk": "cam++",
    "spk_revision": "v1.0.2",
}

# --- Download all models ---
print("Downloading ASR model...")
AutoModel(model=model_names["asr"], model_revision=model_names["asr_revision"], model_path=model_cache_dir)

print("Downloading VAD model...")
AutoModel(model=model_names["vad"], model_revision=model_names["vad_revision"], model_path=model_cache_dir)

print("Downloading Punctuation model...")
AutoModel(model=model_names["punc"], model_revision=model_names["punc_revision"], model_path=model_cache_dir)

print("Downloading Speaker Diarization model...")
AutoModel(model=model_names["spk"], model_revision=model_names["spk_revision"], model_path=model_cache_dir)

print("\nAll models have been downloaded successfully to:")
print(model_cache_dir)

from celery import Celery
import os

# Use environment variables for configuration
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
BROKER_URL = f"redis://{REDIS_HOST}:6379/0"
RESULT_BACKEND_URL = f"redis://{REDIS_HOST}:6379/1"

# This instance is for the worker.
# It needs to 'include' the module containing the tasks.
celery_app = Celery(
    'asr_tasks',
    broker=BROKER_URL,
    backend=RESULT_BACKEND_URL,
    include=['asr_worker']
)

celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,
)

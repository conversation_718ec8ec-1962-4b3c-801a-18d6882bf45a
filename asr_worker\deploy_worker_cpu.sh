#!/bin/bash
# This script builds and deploys the ASR Worker for a CPU-only environment.
# It can launch multiple worker containers on the same machine.
#
# Usage: ./deploy_worker_cpu.sh [API_SERVER_IP] [NUM_WORKERS]
# Example (1 worker): ./deploy_worker_cpu.sh *************
# Example (4 workers): ./deploy_worker_cpu.sh ************* 4

set -e

# --- Step 1: Configuration ---

echo "--- Step 1: Configuring deployment settings ---"

IMAGE_NAME="funasr-worker-cpu-image:latest"
MODEL_CACHE_PATH="/home/<USER>/micro_service/models/fun_asr/iic" 

API_SERVER_IP="**************"
NUM_WORKERS=${2:-1} # Default to 1 worker if not specified

if [ -z "$API_SERVER_IP" ]; then
    echo "Error: API Server IP address is required."
    echo "Usage: ./deploy_worker_cpu.sh [API_SERVER_IP] [NUM_WORKERS]"
    exit 1
fi
echo "API Server IP set to: ${API_SERVER_IP}"
echo "Number of workers to create: ${NUM_WORKERS}"
echo "Host model path set to: ${MODEL_CACHE_PATH}"
echo ""


# --- Step 2: Build Docker Image ---

echo "--- Step 2: Building Docker image ('${IMAGE_NAME}') ---"

docker build --network=host -t "${IMAGE_NAME}" -f Dockerfile.worker.cpu .
echo "Image build complete."
echo ""


# --- Step 3: Start Worker Containers ---

echo "--- Step 3: Starting ${NUM_WORKERS} worker container(s) ---"

for i in $(seq 0 $(($NUM_WORKERS - 1)))
do
  CONTAINER_NAME="asr-worker-cpu-${i}"
  echo "--> Starting worker #${i} with container name: ${CONTAINER_NAME}"

  # Stop and remove if a container with the same name already exists.
  if [ "$(docker ps -a -q -f name=${CONTAINER_NAME})" ]; then
      echo "    Stopping and removing existing container named '${CONTAINER_NAME}'..."
      docker stop "${CONTAINER_NAME}" >/dev/null
      docker rm "${CONTAINER_NAME}" >/dev/null
      echo "    Existing container removed."
  fi

  echo "    Launching new container..."
  docker run -d \
    --name "${CONTAINER_NAME}" \
    -v "${MODEL_CACHE_PATH}:/models:ro" \
    -e "MINIO_ENDPOINT=${API_SERVER_IP}:9000" \
    -e "MINIO_ACCESS_KEY=admin" \
    -e "MINIO_SECRET_KEY=minioadmin" \
    -e "REDIS_HOST=${API_SERVER_IP}" \
    -e "NGPU=0" \
    -e "ASR_MODEL_PATH=/models/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch" \
    -e "VAD_MODEL_PATH=/models/speech_fsmn_vad_zh-cn-16k-common-pytorch" \
    -e "PUNC_MODEL_PATH=/models/punc_ct-transformer_zh-cn-common-vocab272727-pytorch" \
    -e "SPK_MODEL_PATH=/models/speech_campplus_sv_zh-cn_16k-common" \
    --restart always \
    "${IMAGE_NAME}" \
    celery -A celery_app worker --loglevel=info --concurrency=2
done

echo ""
echo "--- Deployment Complete ---"
echo "${NUM_WORKERS} worker(s) started successfully."
echo "To view logs for a specific worker, run: docker logs -f asr-worker-cpu-0"

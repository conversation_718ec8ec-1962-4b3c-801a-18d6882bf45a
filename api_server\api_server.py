import os
import uuid
import logging
from logging.handlers import TimedRotatingFileHandler
from contextlib import asynccontextmanager
from celery.exceptions import TimeoutError
from celery.result import AsyncResult

import uvicorn
from fastapi import FastAPI, File, UploadFile
from fastapi.responses import JSONResponse
from starlette.concurrency import run_in_threadpool
from minio import Minio
from celery_app import celery_app

# --- 日志配置 ---
handler = TimedRotatingFileHandler(
    filename="api_server.log", when="midnight", interval=1, backupCount=15, encoding="utf-8"
)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.addHandler(handler)

# --- MinIO 客户端配置 ---
MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT", "localhost:9000")
MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY", "admin")
MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY", "minioadmin")
MINIO_BUCKET = os.getenv("MINIO_BUCKET", "asr-funasr-audio-bucket")

minio_client = Minio(
    MINIO_ENDPOINT,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=False
)

# --- FastAPI Lifespan Event for Startup ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    # This code runs on startup
    logger.info("Application startup...")
    logger.info(f"Checking for MinIO bucket: '{MINIO_BUCKET}'")
    try:
        found = minio_client.bucket_exists(MINIO_BUCKET)
        if not found:
            minio_client.make_bucket(MINIO_BUCKET)
            logger.info(f"Bucket '{MINIO_BUCKET}' created in MinIO.")
        else:
            logger.info(f"Bucket '{MINIO_BUCKET}' already exists.")
    except Exception as e:
        logger.error(f"Could not connect to MinIO or create bucket on startup: {e}")
        # In a real production scenario, you might want the app to fail hard here
        # or have a retry mechanism. For now, we log the error.
    
    yield
    # This code runs on shutdown
    logger.info("Application shutdown...")

# --- FastAPI 应用 ---
# Assign the lifespan event handler to the app
app = FastAPI(title="ASR API Gateway", lifespan=lifespan)


def wrap_response(data_dict: dict = {}, status=0, msg='success'):
    return JSONResponse(
        status_code=200,
        content={"data": data_dict, 'status': status, 'msg': msg}
    )

@app.post("/llm/asr/recognition")
async def sync_recognition(file: UploadFile = File(..., description="audio file")):
    """
    同步识别接口：接收文件，发布任务，并等待结果返回。
    """
    try:
        suffix = file.filename.split(".")[-1]
        object_name = f"{str(uuid.uuid4())}.{suffix}"
        logger.info(f"Received SYNC request for file: {file.filename}. Object name: {object_name}")

        file_content = await file.read()
        from io import BytesIO
        minio_client.put_object(
            MINIO_BUCKET,
            object_name,
            BytesIO(file_content),
            length=len(file_content),
            content_type=file.content_type
        )
        logger.info(f"File '{object_name}' uploaded to MinIO. Submitting task to queue...")

        task = celery_app.send_task('asr_worker.asr_task', args=[MINIO_BUCKET, object_name])
        logger.info(f"Task {task.id} submitted. Waiting for result...")

        try:
            result = await run_in_threadpool(task.get, timeout=300)
            logger.info(f"Task {task.id} completed. Returning result.")
            return wrap_response(result.get("data", {}), result.get("status", 0), result.get("message", "success"))
        except TimeoutError:
            logger.error(f"Task {task.id} timed out after 300 seconds.")
            return wrap_response({}, -1, "Processing timed out.")
        except Exception as e:
            logger.error(f"Task {task.id} failed with an exception: {e}", exc_info=True)
            return wrap_response({}, -1, f"Task failed: {str(e)}")

    except Exception as e:
        logger.error(f"Error processing sync recognition request: {e}", exc_info=True)
        return wrap_response({}, -1, "Failed to submit recognition task.")


@app.post("/llm/asr/recognition_async")
async def async_recognition(file: UploadFile = File(..., description="audio file")):
    """
    异步识别接口：接收文件，存入MinIO，发布任务到Celery，立即返回任务ID
    """
    try:
        task_id = str(uuid.uuid4())
        suffix = file.filename.split(".")[-1]
        object_name = f"{task_id}.{suffix}"
        logger.info(f"Received ASYNC request for file: {file.filename}. Task ID: {task_id}")

        file_content = await file.read()
        from io import BytesIO
        minio_client.put_object(
            MINIO_BUCKET,
            object_name,
            BytesIO(file_content),
            length=len(file_content),
            content_type=file.content_type
        )
        logger.info(f"File '{object_name}' uploaded to MinIO bucket '{MINIO_BUCKET}'.")

        celery_app.send_task('asr_worker.asr_task', args=[MINIO_BUCKET, object_name], task_id=task_id)
        logger.info(f"Task {task_id} has been sent to the processing queue.")

        return wrap_response(data_dict={"task_id": task_id}, msg="Task received, processing in background.")

    except Exception as e:
        logger.error(f"Error processing async recognition request: {e}", exc_info=True)
        return wrap_response({}, -1, "Failed to submit recognition task.")


@app.get("/llm/asr/result/{task_id}")
async def get_result(task_id: str):
    """
    根据任务ID查询识别结果
    """
    logger.info(f"Querying result for task ID: {task_id}")
    task_result = AsyncResult(task_id, app=celery_app)

    if task_result.state == 'PENDING':
        response_data = {"status": "PENDING", "result": None}
        return wrap_response(response_data, msg="Task is waiting to be processed.")
    elif task_result.state == 'PROGRESS':
        response_data = {"status": "PROCESSING", "result": task_result.info}
        return wrap_response(response_data, msg="Task is currently being processed.")
    elif task_result.state == 'SUCCESS':
        response_data = {"status": "SUCCESS", "result": task_result.result}
        return wrap_response(response_data, msg="Task completed successfully.")
    elif task_result.state == 'FAILURE':
        response_data = {"status": "FAILURE", "result": str(task_result.info)}
        logger.error(f"Task {task_id} failed. Reason: {task_result.info}")
        return wrap_response(response_data, -1, "Task failed during processing.")
    else:
        response_data = {"status": task_result.state, "result": None}
        return wrap_response(response_data, msg="Unknown task state.")


@app.get("/llm/asr/health")
async def health():
    return {'status': 'healthy', 'info': {'celery': celery_app.conf.broker_url, 'minio_bucket': MINIO_BUCKET}}

if __name__ == '__main__':
    uvicorn.run("api_server:app", host="0.0.0.0", port=50101, reload=True)

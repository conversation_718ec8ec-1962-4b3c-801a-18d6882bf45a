from celery import Celery
import os

# Use environment variables for configuration
REDIS_HOST = os.getenv("REDIS_HOST", "redis")
BROKER_URL = f"redis://{REDIS_HOST}:6379/0"
RESULT_BACKEND_URL = f"redis://{REDIS_HOST}:6379/1"

# This instance is for the API server to send tasks.
# It does not need to know about the task implementations.
celery_app = Celery(
    'asr_tasks',
    broker=BROKER_URL,
    backend=RESULT_BACKEND_URL
)

celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,
)
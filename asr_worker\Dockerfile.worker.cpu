# Start from the official FunASR CPU runtime image
FROM registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-cpu-0.4.6

# Set the working directory
WORKDIR /workspace/end_point_server

# --- DYNAMIC FFMPEG PATH SETUP ---
# Find the ffmpeg executable and its libraries, and configure the system to use them.
# This is a robust way to handle architecture-specific paths in the base image.
RUN FFMPEG_BIN_PATH=$(find /workspace -name ffmpeg -type f | head -n 1) && \
    if [ -n "$FFMPEG_BIN_PATH" ]; then \
        echo "Found ffmpeg executable at: $FFMPEG_BIN_PATH" && \
        \
        # 1. Copy the executable to a standard location in the system's PATH
        cp "$FFMPEG_BIN_PATH" /usr/local/bin/ffmpeg && \
        chmod +x /usr/local/bin/ffmpeg && \
        \
        # 2. Find the corresponding 'lib' directory relative to the executable
        FFMPEG_LIB_PATH=$(dirname "$FFMPEG_BIN_PATH")/../lib && \
        echo "Found ffmpeg library path at: $FFMPEG_LIB_PATH" && \
        \
        # 3. Create a config file for the dynamic linker
        echo "$FFMPEG_LIB_PATH" > /etc/ld.so.conf.d/ffmpeg.conf && \
        \
        # 4. Update the linker's cache to make the libraries available system-wide
        ldconfig && \
        \
        echo "Successfully configured ffmpeg executable and libraries."; \
    else \
        echo "Warning: ffmpeg executable not found in /workspace. Assuming it's already in PATH."; \
    fi

# Copy our new, slimmed-down requirements file
COPY worker_requirements_cpu.txt .

# Install our application-specific python packages using a trusted mirror for reliability
RUN pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ -r worker_requirements_cpu.txt

# Copy our application code and configuration into the image
COPY asr_worker.py .
COPY celery_app.py .
COPY hotwords.txt .

# The entrypoint command will be provided by the deploy script
LABEL description="ASR Worker CPU instance based on official FunASR runtime"
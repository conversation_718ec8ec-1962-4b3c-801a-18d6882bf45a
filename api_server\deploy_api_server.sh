#!/bin/bash
# This script builds the API Server image and then starts the container,
# linking it to existing 'redis' and 'minio' containers.
#
# Prerequisites:
# 1. You must be inside the 'api_server' directory.
# 2. Containers named 'redis' and 'minio' must be running.

set -e

# --- 1. Build Docker Image ---
# Added --no-cache to ensure a fresh build and that all pip installs are re-run.
echo "Building 'asr-server-img' from the current directory (forcing a rebuild)..."
docker build --no-cache -t asr-server-img -f Dockerfile.api .
echo "Image 'asr-server-img' built successfully."
echo ""

# --- 2. Start Container ---
CONTAINER_NAME="asr-server-container"
HOST_PORT="50101"
CONTAINER_PORT="50101"
GUNICORN_WORKERS=4

echo "Starting API Server container ('${CONTAINER_NAME}')..."

# Stop and remove the container if it already exists, to ensure a clean start.
if [ "$(docker ps -a -q -f name=${CONTAINER_NAME})" ]; then
    echo "Stopping and removing existing '${CONTAINER_NAME}'..."
    docker stop ${CONTAINER_NAME} >/dev/null
    docker rm ${CONTAINER_NAME} >/dev/null
fi

# --- Configuration ---
REDIS_CONTAINER_NAME="redis"
MINIO_CONTAINER_NAME="minio"
MINIO_USER="admin"
MINIO_PASS="minioadmin"

echo "Attempting to link to '${REDIS_CONTAINER_NAME}' and '${MINIO_CONTAINER_NAME}'..."

GUNICORN_PATH="/usr/local/bin/gunicorn"

docker run -d \
  --name ${CONTAINER_NAME} \
  -p ${HOST_PORT}:${CONTAINER_PORT} \
  --link ${REDIS_CONTAINER_NAME}:redis \
  --link ${MINIO_CONTAINER_NAME}:minio \
  -e "MINIO_ENDPOINT=minio:9000" \
  -e "MINIO_ACCESS_KEY=${MINIO_USER}" \
  -e "MINIO_SECRET_KEY=${MINIO_PASS}" \
  -e "BROKER_URL=redis://${REDIS_CONTAINER_NAME}:6379/0" \
  -e "RESULT_BACKEND_URL=redis://${REDIS_CONTAINER_NAME}:6379/1" \
  --restart always \
  asr-server-img \
  ${GUNICORN_PATH} api_server:app --workers ${GUNICORN_WORKERS} --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:${CONTAINER_PORT}

echo ""
echo "'${CONTAINER_NAME}' started successfully."
echo "To check logs, run: docker logs ${CONTAINER_NAME}"
echo "Access the API documentation at: http://localhost:${HOST_PORT}/docs"